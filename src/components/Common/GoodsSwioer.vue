<template>
  <div class="goods-swiper-container">
    <swiper
      ref="swiperRef"
      :modules="swiperModules"
      :slides-per-view="slidesPerView"
      :space-between="spaceBetween"
      :centered-slides="centeredSlides"
      :loop="enableLoop"
      :autoplay="autoplayConfig"
      :lazy="true"
      :preload-images="false"
      :watch-slides-progress="true"
      :observer="true"
      :observe-parents="true"
      :resistance-ratio="0.85"
      :threshold="5"
      class="goods-swiper"
      :class="[
        `swiper-${mode}`,
        { 'swiper-loading': isLoading }
      ]"
      :style="containerStyle"
      @swiper="onSwiperInit"
      @slide-change="onSlideChange"
      @slide-change-transition-start="onSlideChangeStart"
      @slide-change-transition-end="onSlideChangeEnd"
      @progress="onProgress"
    >
      <swiper-slide
        v-for="(item, index) in imageList"
        :key="`slide-${index}-${item.id || item.url}`"
        class="goods-slide"
        :class="{ 'slide-active': currentIndex === index }"
      >
        <div class="image-container">
          <!-- 加载状态 -->
          <div v-if="!imageLoadStates[index]" class="image-loading">
            <div class="loading-spinner"></div>
          </div>

          <!-- 图片 -->
          <img
            :src="item.url"
            :alt="item.alt || `商品图片${index + 1}`"
            class="goods-image"
            :class="[
              `image-${mode}`,
              getImageClass(item, index)
            ]"
            loading="lazy"
            @load="onImageLoad(index)"
            @error="onImageError(index)"
            @click="onImageClick(item, index)"
          />

          <!-- 错误状态 -->
          <div v-if="imageErrorStates[index]" class="image-error">
            <div class="error-icon">📷</div>
            <span class="error-text">图片加载失败</span>
          </div>
        </div>
      </swiper-slide>
    </swiper>

    <!-- 分页器 -->
    <div
      v-if="showPagination && imageList.length > 1"
      class="pagination-wrapper"
      :class="`pagination-${mode}`"
    >
      <slot
        name="pagination"
        :current-index="currentIndex"
        :total="imageList.length"
        :slide-to="slideTo"
      >
        <!-- 圆点分页器 -->
        <div
          v-if="paginationType === 'dots'"
          class="pagination-dots"
        >
          <span
            v-for="(_, index) in imageList"
            :key="`dot-${index}`"
            class="pagination-dot"
            :class="{ active: currentIndex === index }"
            @click="slideTo(index)"
          ></span>
        </div>

        <!-- 分数分页器 -->
        <div
          v-else-if="paginationType === 'fraction'"
          class="pagination-fraction"
        >
          <span class="fraction-text">
            {{ currentIndex + 1 }} / {{ imageList.length }}
          </span>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Lazy } from 'swiper/modules'
import { debounce, throttle } from 'lodash-es'

// 导入 Swiper 样式
import 'swiper/css'
import 'swiper/css/autoplay'
import 'swiper/css/lazy'

// Props 定义
const props = defineProps({
  // 图片列表
  imageList: {
    type: Array,
    default: () => [],
    validator: (list) => Array.isArray(list)
  },

  // 显示模式：detail(详情页方形) | banner(首页长条)
  mode: {
    type: String,
    default: 'detail',
    validator: (value) => ['detail', 'banner'].includes(value)
  },

  // 自动播放
  autoplay: {
    type: [Boolean, Object],
    default: false
  },

  // 循环播放
  loop: {
    type: Boolean,
    default: true
  },

  // 自定义高度
  height: {
    type: [String, Number],
    default: null
  },

  // 分页器类型：dots | fraction | none
  paginationType: {
    type: String,
    default: 'dots',
    validator: (value) => ['dots', 'fraction', 'none'].includes(value)
  },

  // 是否显示分页器
  showPagination: {
    type: Boolean,
    default: true
  },

  // 预加载图片数量
  preloadImages: {
    type: Number,
    default: 2
  },

  // 是否启用懒加载
  lazy: {
    type: Boolean,
    default: true
  },

  // 自定义断点配置
  breakpoints: {
    type: Object,
    default: () => ({})
  }
})

// Emits 定义
const emit = defineEmits([
  'imageClick',
  'slideChange',
  'swiperInit',
  'imageLoad',
  'imageError'
])

// 响应式数据
const swiperRef = ref(null)
const swiperInstance = ref(null)
const currentIndex = ref(0)
const isLoading = ref(true)
const screenWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 375)
const imageLoadStates = ref({})
const imageErrorStates = ref({})
const imageAspectRatios = ref({})
const isTransitioning = ref(false)
